
//@version=6
indicator("BTC Big Move Hunter", shorttitle="BTC_SNIP", overlay=true, max_boxes_count=50, max_labels_count=50)

// =================== SETTINGS ===================
// FIX: Corrected all `group=` arguments to use direct strings as required by Pine Script v6.

// --- Big Move Settings ---
use_aggressive_entry = input.bool(false, "Use Aggressive Entry Mode", group = "Big Move Settings", tooltip="If true, enters on the first SuperTrend flip instead of waiting for 3-bar stability. Captures trends earlier but may have more false signals.")
min_confluence = input.int(4, "Minimum Confluence", minval = 3, maxval = 7, group = "Big Move Settings", tooltip="Minimum number of confirming conditions required for a signal.")
trend_strength_required = input.int(20, "Minimum ADX for Strong Trend", minval = 15, maxval = 40, group = "Big Move Settings", tooltip="Minimum ADX value on 1H timeframe to consider the trend strong.")
signal_cooldown = input.int(10, "Signal Cooldown (bars)", minval = 5, maxval = 50, group = "Big Move Settings", tooltip="Minimum number of bars to wait before a new signal in the same direction can appear.")
volume_multiplier = input.float(1.5, "Volume Surge Multiplier", minval = 1.2, maxval = 2.5, step = 0.1, group = "Big Move Settings", tooltip="Multiplier for average volume to detect a volume surge.")

// --- Entry Settings ---
entry_timeframe = input.string("15", "Entry Timeframe RSI", options=["1", "3", "5", "15"], group = "Entry Settings", tooltip="Timeframe used for Entry RSI and primary SuperTrend.")
use_entry_confirmation = input.bool(true, "Use Entry Confirmation", group = "Entry Settings", tooltip="If true, an additional confirmation condition must be met for the signal.")
confirm_type = input.string("Structure Break", "Confirmation Type", options=["Structure Break", "RSI Cross", "Engulfing"], group = "Entry Settings", tooltip="Type of confirmation: Market structure break, RSI crossing 50, or Engulfing candle pattern.")

// --- Visual Settings (Refined) ---
show_signals = input.bool(true, "Show Entry Signals", group = "Display Settings", tooltip="Show/Hide Buy/Sell signal arrows on the chart.")
show_table = input.bool(false, "Show Information Table", group = "Display Settings", tooltip="Show/Hide the debug information table.")
show_structure = input.bool(true, "Show Market Structure", group = "Display Settings", tooltip="Show/Hide SuperTrend line, EMA 200, and background colors.")
show_signal_strength = input.bool(true, "Show Signal Strength", group = "Display Settings", tooltip="Show/Hide signal strength text with signals.")
show_tsl_details = input.bool(true, "Show TSL Details on Chart", group = "Display Settings", tooltip="Show/Hide ATR TSL line, ATR TSL exit markers, and SuperTrend TSL exit markers/lines.")
show_chop_filter = input.bool(true, "Show Chop Filter Overlay", group = "Display Settings", tooltip="Colors the background gray when trades are blocked by the chop filter.")
// REFINEMENT: Add MA Ribbon for visual context, directly integrated.
show_ma_ribbon = input.bool(true, "Show MA Momentum Ribbon", group = "Display Settings", tooltip="Show/Hide the Fast/Slow EMA ribbon for visual momentum context.")
ma_fast_len = input.int(9, "MA Ribbon Fast Length", group = "Display Settings", minval=1)
ma_slow_len = input.int(21, "MA Ribbon Slow Length", group = "Display Settings", minval=1)

// --- Chop Filter Settings ---
use_chop_filter = input.bool(true, "Use Chop Filter", group = "Chop Filter", tooltip="Enable the chop filter to avoid trading in sideways markets.")
chop_score_threshold = input.int(2, "Chop Score Threshold", group = "Chop Filter", minval=1, maxval=4, tooltip="Block trades if the number of active chop conditions meets or exceeds this value. Default of 2 is more aggressive.")
chop_filter_threshold = input.float(61.8, "Choppiness Index Threshold", group = "Chop Filter", minval=0, maxval=100, step=0.1)
adx_chop_threshold = input.int(15, "ADX Chop Threshold", group = "Chop Filter", minval=10, maxval=25)
flat_ma_threshold = input.float(0.0005, "Flat MA Threshold (%)", group = "Chop Filter", minval=0.0001, maxval=0.01, step=0.0001)
bbw_atr_ratio_threshold = input.float(1.0, "BBW/ATR Ratio Threshold", group = "Chop Filter", minval=0.1, maxval=3.0, step=0.1)

// --- Risk Management Settings (Refined) ---
use_trailing_stop = input.bool(true, "Use Trailing Stop", group = "Risk Management", tooltip="Enable ATR-based Trailing Stop Loss.")
trailing_activation = input.float(3.0, "Trailing Activation (R:R)", minval=1.5, maxval=5.0, step=0.5, group = "Risk Management", tooltip="R:R ratio (based on initial SL) at which the ATR Trailing Stop activates.")
atr_tsl_multiplier = input.float(2.5, "ATR TSL Multiplier (Standard)", minval=1.0, maxval=5.0, step=0.1, group = "Risk Management", tooltip="ATR multiplier for TSL in normal conditions. Higher values make it looser.")
initial_risk_atr_multiplier = input.float(2.5, "Initial Risk ATR Multiplier", minval=1.0, maxval=5.0, step=0.1, group = "Risk Management", tooltip="ATR multiplier for calculating the initial conceptual stop distance (risk amount).")
atr_tsl_period = input.int(40, "ATR TSL Period", minval=10, maxval=100, group = "Risk Management", tooltip="ATR period for calculating the ATR trailing stop distance. Longer periods are less sensitive.")
tsl_exit_on_close = input.bool(false, "TSL Exit on Candle Close", group = "Risk Management", tooltip="If true, ATR TSL requires a candle close beyond the TSL to trigger an exit. If false, exits on wick touch.")
atr_tsl_strong_trend_multiplier = input.float(3.5, "ATR TSL Multiplier (Strong Trend)", minval=1.5, maxval=7.0, step=0.1, group = "Risk Management", tooltip="ATR multiplier for TSL when a strong trend is detected. Should be >= Standard Multiplier.")
// REFINEMENT: Add Confluence Decay Exit setting
use_confluence_decay_exit = input.bool(true, "Use Confluence Decay Exit", group = "Risk Management", tooltip="If true, an active trade will exit if the confluence score for its direction drops below a threshold, indicating the trade's premise is weakening.")
confluence_exit_threshold = input.int(2, "Confluence Exit Threshold", group = "Risk Management", minval=1, maxval=4, tooltip="The confluence score below which a trade will be exited.")

// --- Momentum Filter ---
stoch_k = input.int(5, "Stochastic K Period", minval=3, maxval=14, group = "Momentum Filter")
stoch_d = input.int(3, "Stochastic D Period", minval=2, maxval=5, group = "Momentum Filter")

// --- V1 Constants ---
V1_SCORE_DIFF_THRESHOLD = 1.5 // Minimum score difference for a V1 style signal

// --- Higher-Timeframe Filter ---
filter_type = input.string("None", "Higher Timeframe Filter", options=["None", "DailyEMA", "WeeklyEMA", "Donchian"], group = "Confluence Extras", tooltip="Apply an additional filter based on higher timeframe analysis.")
daily_ema_len = input.int(21, "Daily EMA Length", minval=5, group = "Confluence Extras", tooltip="Length for Daily EMA filter.")
weekly_ema_len = input.int(10, "Weekly EMA Length", minval=3, group = "Confluence Extras", tooltip="Length for Weekly EMA filter.")
donchian_len = input.int(20, "Donchian Channel Length", minval=5, group = "Confluence Extras", tooltip="Length for Donchian Channel breakout filter.")

// =================== CALCULATIONS (Original Structure) ===================
// --- Higher Timeframe Calculations (Original) ---
daily_ema_val = request.security(syminfo.tickerid, "D", ta.ema(close, daily_ema_len), lookahead=barmerge.lookahead_off)
weekly_ema_val = request.security(syminfo.tickerid, "W", ta.ema(close, weekly_ema_len), lookahead=barmerge.lookahead_off)
daily_donchian_upper = request.security(syminfo.tickerid, "D", ta.highest(high, donchian_len), lookahead=barmerge.lookahead_off)
daily_donchian_lower = request.security(syminfo.tickerid, "D", ta.lowest(low, donchian_len), lookahead=barmerge.lookahead_off)

daily_bullish = close > daily_ema_val
weekly_bullish = close > weekly_ema_val
donch_break_long = close > daily_donchian_upper[1]
donch_break_short = close < daily_donchian_lower[1]

// --- Trend Analysis (Original) ---
ema_50_1h = request.security(syminfo.tickerid, "60", ta.ema(close, 50), lookahead=barmerge.lookahead_off)
ema_200_1h = request.security(syminfo.tickerid, "60", ta.ema(close, 200), lookahead=barmerge.lookahead_off)
ema_200_4h = request.security(syminfo.tickerid, "240", ta.ema(close, 200), lookahead=barmerge.lookahead_off)

trend_1h = close > ema_200_1h ? 1 : -1
trend_4h = close > ema_200_4h ? 1 : -1
trend_alignment = math.abs(trend_1h + trend_4h)
major_trend_direction = (trend_1h + trend_4h) > 0 ? 1 : -1
strong_trend_condition = trend_alignment >= 2

// --- Momentum Indicators (Original) ---
rsi_entry = request.security(syminfo.tickerid, entry_timeframe, ta.rsi(close, 14), lookahead=barmerge.lookahead_off)
rsi_15m = request.security(syminfo.tickerid, "15", ta.rsi(close, 14), lookahead=barmerge.lookahead_off)
rsi_1h = request.security(syminfo.tickerid, "60", ta.rsi(close, 14), lookahead=barmerge.lookahead_off)
rsi_cross_up = ta.crossover(rsi_entry, 50)
rsi_cross_down = ta.crossunder(rsi_entry, 50)
k = ta.stoch(close, high, low, stoch_k)
d = ta.sma(k, stoch_d)
momentum_bullish = k > 20
momentum_bearish = k < 80
[macd_line_1h, signal_line_1h, hist_1h] = request.security(syminfo.tickerid, "60", ta.macd(close, 12, 26, 9), lookahead=barmerge.lookahead_off)

// --- ADX Calculation (Original) ---
adx_calc(len) =>
    up = ta.change(high)
    down = -ta.change(low)
    plusDM_raw = up > down and up > 0 ? up : 0
    minusDM_raw = down > up and down > 0 ? down : 0
    plusDM = na(up) ? na : plusDM_raw
    minusDM = na(down) ? na : minusDM_raw
    truerange = ta.rma(ta.tr, len)
    smoothed_plusDM = ta.rma(plusDM, len)
    smoothed_minusDM = ta.rma(minusDM, len)
    plus = fixnan(100 * smoothed_plusDM / truerange)
    minus = fixnan(100 * smoothed_minusDM / truerange)
    sum = plus + minus
    dx = sum == 0 ? 0 : math.abs(plus - minus) / sum
    adx = 100 * ta.rma(dx, len)
    adx
adx_1h = request.security(syminfo.tickerid, "60", adx_calc(14), lookahead=barmerge.lookahead_off)

// --- SuperTrend Indicators (Original) ---
[st_entry, direction_entry] = request.security(syminfo.tickerid, entry_timeframe, ta.supertrend(3, 10), lookahead=barmerge.lookahead_off)
[st_15m, direction_15m] = request.security(syminfo.tickerid, "15", ta.supertrend(3, 10), lookahead=barmerge.lookahead_off)
[st_1h, direction_1h] = request.security(syminfo.tickerid, "60", ta.supertrend(3, 10), lookahead=barmerge.lookahead_off)
st_bullish = direction_entry == -1
st_bearish = direction_entry == 1
st_15m_bullish = direction_15m == -1
st_1h_bullish = direction_1h == -1
st_stable_bullish = st_bullish and st_bullish[1] and st_bullish[2]
st_stable_bearish = st_bearish and st_bearish[1] and st_bearish[2]
st_alignment_bull = (st_bullish ? 1 : 0) + (st_15m_bullish ? 1 : 0) + (st_1h_bullish ? 1 : 0)
st_alignment_bear = (st_bearish ? 1 : 0) + (not st_15m_bullish ? 1 : 0) + (not st_1h_bullish ? 1 : 0)

// --- Volume Analysis (Original) ---
volume_ma = ta.sma(volume, 20)
volume_surge = volume > volume_ma * volume_multiplier

// REFINEMENT: Add MA Ribbon calculations for visual plotting
ma_fast = ta.ema(close, ma_fast_len)
ma_slow = ta.ema(close, ma_slow_len)

// --- Performance Optimization (Original) ---
should_calculate = bar_index > 50

// All V1 Logic is preserved exactly as you wrote it
// =================== V1 PIVOT-BASED MARKET STRUCTURE ===================
v1_pivot_high_val = ta.pivothigh(high, 15, 15)
v1_pivot_low_val = ta.pivotlow(low, 15, 15)
var v1_recent_highs = array.new<float>()
var v1_recent_lows = array.new<float>()
if not na(v1_pivot_high_val)
    array.push(v1_recent_highs, v1_pivot_high_val)
    if array.size(v1_recent_highs) > 3
        array.shift(v1_recent_highs)
if not na(v1_pivot_low_val)
    array.push(v1_recent_lows, v1_pivot_low_val)
    if array.size(v1_recent_lows) > 3
        array.shift(v1_recent_lows)
v1_market_structure = "RANGING"
v1_structure_strength_adjust = 0
if array.size(v1_recent_highs) >= 2 and array.size(v1_recent_lows) >= 2
    v1_hh_count = 0
    v1_hl_count = 0
    v1_lh_count = 0
    v1_ll_count = 0
    if array.size(v1_recent_highs) > 1
        for i = 1 to array.size(v1_recent_highs) - 1
            if array.get(v1_recent_highs, i) > array.get(v1_recent_highs, i - 1)
                v1_hh_count += 1
            else
                v1_lh_count += 1
    if array.size(v1_recent_lows) > 1
        for i = 1 to array.size(v1_recent_lows) - 1
            if array.get(v1_recent_lows, i) > array.get(v1_recent_lows, i - 1)
                v1_hl_count += 1
            else
                v1_ll_count += 1
    v1_bullish_signals = v1_hh_count + v1_hl_count
    v1_bearish_signals = v1_lh_count + v1_ll_count
    if v1_bullish_signals > v1_bearish_signals + 1
        v1_market_structure := "BULLISH"
        v1_structure_strength_adjust := 5
    else if v1_bearish_signals > v1_bullish_signals + 1
        v1_market_structure := "BEARISH"
        v1_structure_strength_adjust := -5

// =================== V1 VOLATILITY REGIME & DYNAMIC RSI ===================
atr_short = ta.atr(10)
atr_long = ta.atr(40)
volatility_ratio = atr_short / atr_long
volatility_regime = switch
    volatility_ratio < 0.8 => "LOW"
    volatility_ratio < 1.2 => "MEDIUM"
    volatility_ratio < 1.5 => "HIGH"
    => "EXTREME"
min_confluence_dynamic = switch volatility_regime
    "LOW" => min_confluence + 1
    "MEDIUM" => min_confluence
    "HIGH" => min_confluence - 1
    "EXTREME" => min_confluence - 1
v1_volatility_regime = "MEDIUM"
v1_rsi_buy_threshold = 25
v1_rsi_sell_threshold = 75
v1_vol_adjustment = 0
float v1_vol_percentile = na
if should_calculate
    v1_returns = math.log(close / close[1])
    v1_long_vol = ta.stdev(v1_returns, 200)
    v1_vol_percentile := ta.percentrank(v1_long_vol, 200)
    v1_volatility_regime := switch
        v1_vol_percentile < 25 => "LOW"
        v1_vol_percentile < 50 => "MEDIUM"
        v1_vol_percentile < 80 => "HIGH"
        => "EXTREME"
    v1_base_buy_rsi = 30
    v1_base_sell_rsi = 70
    v1_vol_adjustment := switch v1_volatility_regime
        "LOW" => 5
        "HIGH" => -5
        "EXTREME" => -10
        => 0
    v1_rsi_buy_threshold := math.max(15, math.min(45, v1_base_buy_rsi + v1_vol_adjustment + v1_structure_strength_adjust))
    v1_rsi_sell_threshold := math.min(85, math.max(55, v1_base_sell_rsi + v1_vol_adjustment + v1_structure_strength_adjust))

// =================== CHOP / SIDEWAYS MARKET FILTERS (Original) ===================
chopIdx = math.log10(math.sum(ta.tr, 14) / (ta.highest(high, 14) - ta.lowest(low, 14))) * 100
flatMA_val = ema_200_1h > 0 ? math.abs(ta.change(ema_200_1h, 10) / ema_200_1h) : 0
[bb_middle, bb_upper, bb_lower] = ta.bb(close, 20, 2)
bb_width = bb_upper - bb_lower
bbw_atr_ratio = atr_short > 0 ? (bb_width / atr_short) : 0
is_choppy_idx = chopIdx > chop_filter_threshold
is_adx_weak = adx_1h < adx_chop_threshold
is_ma_flat = flatMA_val < flat_ma_threshold
is_bbw_tight = bbw_atr_ratio < bbw_atr_ratio_threshold
chop_score = (is_choppy_idx ? 1 : 0) + (is_adx_weak ? 1 : 0) + (is_ma_flat ? 1 : 0) + (is_bbw_tight ? 1 : 0)
trade_block = use_chop_filter and (chop_score >= chop_score_threshold)

// =================== SIGNAL LOGIC (Original) ===================
var string last_signal = ""
var int bars_since_signal = 0
bars_since_signal += 1
var bool shared_condition_adx_strong = false
var bool shared_condition_volume_surge = false
max_confluence = 0

// =================== BUY SIGNAL CONDITIONS (Original) ===================
v1_buy_score = 0.0
v1_actual_buy_confluence_count = 0
var bool condition_st_stable_bullish_aligned = false
var bool condition_st_bullish_aligned = false
var bool condition_st_stable_bearish_aligned = false
var bool condition_st_bearish_aligned = false
if should_calculate
    v1_buy_score := 0.0
    v1_actual_buy_confluence_count := 0
    shared_condition_adx_strong := not na(adx_1h) and adx_1h >= trend_strength_required
    shared_condition_volume_surge := volume_surge
    condition_major_uptrend = major_trend_direction == 1
    if condition_major_uptrend
        v1_buy_score += 1.5
        v1_actual_buy_confluence_count += 1
    actual_rsi_entry_buy_cond = rsi_entry < v1_rsi_buy_threshold
    condition_rsi_buy = not na(rsi_entry) and not na(rsi_15m) and not na(rsi_1h) and actual_rsi_entry_buy_cond and rsi_15m < 85 and rsi_1h < 80
    if condition_rsi_buy
        v1_buy_score += 2.0
        v1_actual_buy_confluence_count += 1
    condition_macd_bullish = not na(macd_line_1h) and not na(signal_line_1h) and macd_line_1h > signal_line_1h
    if condition_macd_bullish
        v1_buy_score += 1.0
        v1_actual_buy_confluence_count += 1
    condition_st_stable_bullish_aligned := st_stable_bullish and st_alignment_bull >= 2
    condition_st_bullish_aligned := st_bullish and st_alignment_bull >= 2
    if condition_st_stable_bullish_aligned
        v1_buy_score += 2.0
        v1_actual_buy_confluence_count += 1
    else if condition_st_bullish_aligned
        v1_buy_score += 1.0
        v1_actual_buy_confluence_count += 1
    if shared_condition_adx_strong
        v1_buy_score += 1.0
        v1_actual_buy_confluence_count += 1
    if shared_condition_volume_surge
        v1_buy_score += 0.5
        v1_actual_buy_confluence_count += 1
    condition_price_action_bullish = close > open and close > high[1]
    if condition_price_action_bullish
        v1_buy_score += 0.5
        v1_actual_buy_confluence_count += 1
    condition_v1_structure_bullish = v1_market_structure == "BULLISH"
    if condition_v1_structure_bullish
        v1_buy_score += 1.0
        v1_actual_buy_confluence_count += 1

// =================== SELL SIGNAL CONDITIONS (Original) ===================
v1_sell_score = 0.0
v1_actual_sell_confluence_count = 0
if should_calculate
    v1_sell_score := 0.0
    v1_actual_sell_confluence_count := 0
    condition_major_downtrend = major_trend_direction == -1
    if condition_major_downtrend
        v1_sell_score += 1.5
        v1_actual_sell_confluence_count += 1
    actual_rsi_entry_sell_cond = rsi_entry > v1_rsi_sell_threshold
    condition_rsi_sell = not na(rsi_entry) and not na(rsi_15m) and not na(rsi_1h) and actual_rsi_entry_sell_cond and rsi_15m > 15 and rsi_1h > 20
    if condition_rsi_sell
        v1_sell_score += 2.0
        v1_actual_sell_confluence_count += 1
    condition_macd_bearish = not na(macd_line_1h) and not na(signal_line_1h) and macd_line_1h < signal_line_1h
    if condition_macd_bearish
        v1_sell_score += 1.0
        v1_actual_sell_confluence_count += 1
    condition_st_stable_bearish_aligned := st_stable_bearish and st_alignment_bear >= 2
    condition_st_bearish_aligned := st_bearish and st_alignment_bear >= 2
    if condition_st_stable_bearish_aligned
        v1_sell_score += 2.0
        v1_actual_sell_confluence_count += 1
    else if condition_st_bearish_aligned
        v1_sell_score += 1.0
        v1_actual_sell_confluence_count += 1
    if shared_condition_adx_strong
        v1_sell_score += 1.0
        v1_actual_sell_confluence_count += 1
    if shared_condition_volume_surge
        v1_sell_score += 0.5
        v1_actual_sell_confluence_count += 1
    condition_price_action_bearish = close < open and close < low[1]
    if condition_price_action_bearish
        v1_sell_score += 0.5
        v1_actual_sell_confluence_count += 1
    condition_v1_structure_bearish = v1_market_structure == "BEARISH"
    if condition_v1_structure_bearish
        v1_sell_score += 1.0
        v1_actual_sell_confluence_count += 1

// =================== ENHANCED SIGNAL GENERATION (Original) ===================
signal_direction = "NONE"
v1_score_diff = 0.0
current_signal_strength_text = "WEAK"
current_signal_strength_score = 0
current_max_confluence = 0
if should_calculate
    v1_score_diff := math.abs(v1_buy_score - v1_sell_score)
    bool st_buy_condition_met = use_aggressive_entry ? st_bullish : st_stable_bullish
    bool st_sell_condition_met = use_aggressive_entry ? st_bearish : st_stable_bearish
    if v1_buy_score > v1_sell_score
        current_max_confluence := v1_actual_buy_confluence_count
        current_signal_strength_text := switch
            current_max_confluence >= 6 and v1_score_diff >= 4.0 => "ULTRA"
            current_max_confluence >= 5 and v1_score_diff >= 3.0 => "STRONG"
            current_max_confluence >= 4 and v1_score_diff >= V1_SCORE_DIFF_THRESHOLD => "MODERATE"
            => "WEAK"
        if current_max_confluence >= min_confluence and v1_score_diff >= V1_SCORE_DIFF_THRESHOLD and current_signal_strength_text != "WEAK" and st_buy_condition_met
            signal_direction := "BUY"
    else if v1_sell_score > v1_buy_score
        current_max_confluence := v1_actual_sell_confluence_count
        current_signal_strength_text := switch
            current_max_confluence >= 6 and v1_score_diff >= 4.0 => "ULTRA"
            current_max_confluence >= 5 and v1_score_diff >= 3.0 => "STRONG"
            current_max_confluence >= 4 and v1_score_diff >= V1_SCORE_DIFF_THRESHOLD => "MODERATE"
            => "WEAK"
        if current_max_confluence >= min_confluence and v1_score_diff >= V1_SCORE_DIFF_THRESHOLD and current_signal_strength_text != "WEAK" and st_sell_condition_met
            signal_direction := "SELL"
    current_signal_strength_score := current_signal_strength_text == "ULTRA" ? 4 : current_signal_strength_text == "STRONG" ? 3 : current_signal_strength_text == "MODERATE" ? 2 : 1
if signal_direction == "NONE" and should_calculate
    current_max_confluence := math.max(v1_actual_buy_confluence_count, v1_actual_sell_confluence_count)
max_confluence := current_max_confluence

// =================== ENTRY CONFIRMATION (Original) ===================
entry_confirmed = true
if use_entry_confirmation and should_calculate and signal_direction != "NONE"
    if confirm_type == "Structure Break"
        entry_confirmed := signal_direction == "BUY" ? close > high[1] : close < low[1]
    else if confirm_type == "RSI Cross"
        entry_confirmed := signal_direction == "BUY" ? rsi_cross_up : rsi_cross_down
    else if confirm_type == "Engulfing"
        entry_confirmed := signal_direction == "BUY" ? close > open and close > high[1] : close < open and close < low[1]

// =================== FINAL SIGNAL VALIDATION (Original) ===================
daily_filter_active = filter_type == "DailyEMA"
weekly_filter_active = filter_type == "WeeklyEMA"
donchian_filter_active = filter_type == "Donchian"
buy_htf_conditions_met = (not daily_filter_active or daily_bullish) and (not weekly_filter_active or weekly_bullish) and (not donchian_filter_active or donch_break_long)
sell_htf_conditions_met = (not daily_filter_active or not daily_bullish) and (not weekly_filter_active or not weekly_bullish) and (not donchian_filter_active or donch_break_short)
htf_filter_check = if filter_type == "None"
    true
else if signal_direction == "BUY"
    buy_htf_conditions_met
else if signal_direction == "SELL"
    sell_htf_conditions_met
else
    true
st_check_passed = (signal_direction == "BUY" and (condition_st_stable_bullish_aligned or condition_st_bullish_aligned)) or (signal_direction == "SELL" and (condition_st_stable_bearish_aligned or condition_st_bearish_aligned))
ranging_market_filter_passed = v1_market_structure != "RANGING"
big_move_signal = should_calculate and signal_direction != "NONE" and bars_since_signal >= signal_cooldown and signal_direction != last_signal and entry_confirmed and htf_filter_check and st_check_passed and ranging_market_filter_passed
final_buy_signal = not trade_block and big_move_signal and momentum_bullish and (current_max_confluence >= min_confluence_dynamic)
final_sell_signal = not trade_block and big_move_signal and momentum_bearish and (current_max_confluence >= min_confluence_dynamic)

// REFINEMENT: This entire section is removed as it's replaced by a new, cleaner state management system below.
// =================== NON-REPAINTING SIGNAL & TSL MANAGEMENT =================== (REMOVED)
// var bool plot_confirmed_signal_now = false ... etc. (All lines from here to the next major section are replaced)

// =================== STATE MANAGEMENT & TSL (Refined) ===================
// This new section manages the trade state and all exit conditions in one place.
var trade_state = "NONE" // Can be NONE, LONG, or SHORT
var entry_price = 0.0
var initial_risk_for_tsl = 0.0
var exit_reason_text = ""

// --- Exit Conditions ---
st_flip_exit = (trade_state == "LONG" and st_bearish) or (trade_state == "SHORT" and st_bullish)

atr_tsl_hit = false
var atr_tsl_level = 0.0
if use_trailing_stop and trade_state != "NONE"
    current_profit = trade_state == "LONG" ? close - entry_price : entry_price - close
    current_rr = initial_risk_for_tsl > 0 ? current_profit / initial_risk_for_tsl : 0.0
    
    // Determine which multiplier to use
    effective_tsl_multiplier = (trade_state == "LONG" and major_trend_direction == 1 and shared_condition_adx_strong) or (trade_state == "SHORT" and major_trend_direction == -1 and shared_condition_adx_strong) ? atr_tsl_strong_trend_multiplier : atr_tsl_multiplier
    
    if current_rr >= trailing_activation
        atr_val = ta.atr(atr_tsl_period) * effective_tsl_multiplier
        if trade_state == "LONG"
            new_tsl = close - atr_val
            atr_tsl_level := atr_tsl_level == 0.0 ? new_tsl : math.max(atr_tsl_level, new_tsl) // Initialize if not set
            if (tsl_exit_on_close ? close : low) <= atr_tsl_level
                atr_tsl_hit := true
        else // SHORT
            new_tsl = close + atr_val
            atr_tsl_level := atr_tsl_level == 0.0 ? new_tsl : math.min(atr_tsl_level, new_tsl) // Initialize if not set
            if (tsl_exit_on_close ? close : high) >= atr_tsl_level
                atr_tsl_hit := true
else
    atr_tsl_level := 0.0 // Reset TSL when not in a trade or not activated

confluence_decay_exit = false
if use_confluence_decay_exit and trade_state != "NONE"
    active_trade_confluence = trade_state == "LONG" ? v1_actual_buy_confluence_count : v1_actual_sell_confluence_count
    if active_trade_confluence < confluence_exit_threshold
        confluence_decay_exit := true

// --- Final Exit Logic ---
exit_long = trade_state == "LONG" and (st_flip_exit or atr_tsl_hit or confluence_decay_exit)
exit_short = trade_state == "SHORT" and (st_flip_exit or atr_tsl_hit or confluence_decay_exit)

// --- State Machine ---
if exit_long or exit_short
    trade_state := "NONE"
    exit_reason_text := atr_tsl_hit ? "ATR TSL" : st_flip_exit ? "ST FLIP" : "DECAY"
    bars_since_signal := 0 // Reset cooldown on exit

if final_buy_signal and trade_state == "NONE"
    trade_state := "LONG"
    entry_price := close
    initial_risk_for_tsl := ta.atr(atr_tsl_period) * initial_risk_atr_multiplier
    last_signal := "BUY"
    bars_since_signal := 0
else if final_sell_signal and trade_state == "NONE"
    trade_state := "SHORT"
    entry_price := close
    initial_risk_for_tsl := ta.atr(atr_tsl_period) * initial_risk_atr_multiplier
    last_signal := "SELL"
    bars_since_signal := 0

// =================== VISUAL ELEMENTS (Refined) ===================
st_bg_color = st_bullish ? color.new(color.green, 95) : color.new(color.red, 95)
bgcolor(show_structure ? st_bg_color : na, title="SuperTrend Background")
bgcolor(show_chop_filter and trade_block ? color.new(color.gray, 85) : na, title="Chop Block")
bgcolor(not ranging_market_filter_passed ? color.new(color.yellow, 92) : na, title="Ranging Market Block")

// --- MA Ribbon Plot ---
p_fast = plot(show_ma_ribbon ? ma_fast : na, "MA Ribbon Fast", color.new(color.yellow, 0))
p_slow = plot(show_ma_ribbon ? ma_slow : na, "MA Ribbon Slow", color.new(color.purple, 0))
fill(p_fast, p_slow, color=ma_fast > ma_slow ? color.new(color.yellow, 85) : color.new(color.purple, 85), title="MA Ribbon Fill")

// --- Original Plots (Preserved) ---
plot(show_structure ? st_entry : na, "SuperTrend", st_bullish ? color.new(color.green, 0) : color.new(color.red, 0), linewidth=2, style=plot.style_line)
plot(show_structure ? ema_200_1h : na, "EMA 200", color.new(color.orange, 20), linewidth=2)

// --- Signal & Exit Plots ---
plotshape(final_buy_signal and trade_state[1] == "NONE", title="BUY Signal", style=shape.triangleup, location=location.belowbar, color=color.new(color.lime, 0), size=size.large)
plotshape(final_sell_signal and trade_state[1] == "NONE", title="SELL Signal", style=shape.triangledown, location=location.abovebar, color=color.new(color.red, 0), size=size.large)

plotshape(exit_long, title="Exit BUY", style=shape.labeldown, location=location.abovebar, color=color.new(color.gray, 0), text=exit_reason_text, textcolor=color.white, size=size.normal)
plotshape(exit_short, title="Exit SELL", style=shape.labelup, location=location.belowbar, color=color.new(color.gray, 0), text=exit_reason_text, textcolor=color.white, size=size.normal)

// --- TSL Plot ---
plot(trade_state != "NONE" and use_trailing_stop and atr_tsl_level != 0.0 ? atr_tsl_level : na, "ATR Trailing SL", color=color.new(color.blue, 50), style=plot.style_cross, linewidth=2)

// =================== DEBUG TABLE (Original) ===================
if show_table and barstate.islast
    var table debug_table = table.new(position.top_right, 2, 22, bgcolor=color.white, border_width=1)
    if barstate.islast
        table.cell(debug_table, 0, 0, "BTC SNIPER v2.4.1", text_color=color.white, bgcolor=color.navy)
        table.cell(debug_table, 1, 0, "Status", text_color=color.white, bgcolor=color.navy)
        table.cell(debug_table, 0, 1, "ST Entry", text_color=color.black)
        table.cell(debug_table, 1, 1, st_bullish ? "BULL" : "BEAR", text_color=st_bullish ? color.green : color.red)
        table.cell(debug_table, 0, 2, "ST Stable", text_color=color.black)
        table.cell(debug_table, 1, 2, st_stable_bullish ? "BULL" : (st_stable_bearish ? "BEAR" : "MIXED"), text_color=st_stable_bullish ? color.green : (st_stable_bearish ? color.red : color.orange))
        table.cell(debug_table, 0, 3, "ST Alignment", text_color=color.black)
        table.cell(debug_table, 1, 3, str.tostring(math.max(st_alignment_bull, st_alignment_bear)) + "/3", text_color=math.max(st_alignment_bull, st_alignment_bear) >= 2 ? color.green : color.orange)
        table.cell(debug_table, 0, 4, "Major Trend", text_color=color.black)
        table.cell(debug_table, 1, 4, major_trend_direction == 1 ? "BULL" : "BEAR", text_color=major_trend_direction == 1 ? color.green : color.red)
        table.cell(debug_table, 0, 5, "RSI Entry", text_color=color.black)
        table.cell(debug_table, 1, 5, str.tostring(math.round(rsi_entry)), text_color=rsi_entry > 50 ? color.green : color.red)
        table.cell(debug_table, 0, 6, "MACD 1H", text_color=color.black)
        table.cell(debug_table, 1, 6, macd_line_1h > signal_line_1h ? "BULL" : "BEAR", text_color=macd_line_1h > signal_line_1h ? color.green : color.red)
        table.cell(debug_table, 0, 7, "ADX 1H", text_color=color.black)
        table.cell(debug_table, 1, 7, str.tostring(math.round(adx_1h)), text_color=adx_1h >= trend_strength_required ? color.green : color.gray)
        table.cell(debug_table, 0, 8, "Volume", text_color=color.black)
        table.cell(debug_table, 1, 8, volume_surge ? "SURGE" : "NORMAL", text_color=volume_surge ? color.green : color.gray)
        table.cell(debug_table, 0, 9, "Buy Conf", text_color=color.black)
        table.cell(debug_table, 1, 9, str.tostring(v1_actual_buy_confluence_count) + "/" + str.tostring(min_confluence), text_color=v1_actual_buy_confluence_count >= min_confluence ? color.green : color.gray)
        table.cell(debug_table, 0, 10, "Sell Conf", text_color=color.black)
        table.cell(debug_table, 1, 10, str.tostring(v1_actual_sell_confluence_count) + "/" + str.tostring(min_confluence), text_color=v1_actual_sell_confluence_count >= min_confluence ? color.red : color.gray)
        table.cell(debug_table, 0, 11, "Signal Dir", text_color=color.black)
        table.cell(debug_table, 1, 11, signal_direction, text_color=signal_direction != "NONE" ? color.blue : color.gray)
        table.cell(debug_table, 0, 12, "Entry Confirm", text_color=color.black)
        table.cell(debug_table, 1, 12, entry_confirmed ? "YES" : "NO", text_color=entry_confirmed ? color.green : color.red)
        table.cell(debug_table, 0, 13, "Final Signal", text_color=color.black)
        table.cell(debug_table, 1, 13, (final_buy_signal or final_sell_signal) ? signal_direction : "WAITING", text_color=(final_buy_signal or final_sell_signal) ? color.green : color.gray)
        table.cell(debug_table, 0, 14, "Chop Score", text_color=color.black)
        table.cell(debug_table, 1, 14, str.tostring(chop_score) + "/" + str.tostring(chop_score_threshold) + (trade_block ? " (BLOCK)" : ""), text_color=trade_block ? color.red : color.green)
        table.cell(debug_table, 0, 15, "V1 Pivot Str.", text_color=color.black, bgcolor=v1_market_structure == "RANGING" and not ranging_market_filter_passed ? color.new(color.yellow, 70) : color.white)
        table.cell(debug_table, 1, 15, v1_market_structure + (not ranging_market_filter_passed ? " (BLOCK)" : ""), text_color=v1_market_structure == "BULLISH" ? color.green : (v1_market_structure == "BEARISH" ? color.red : color.orange))
        table.cell(debug_table, 0, 16, "V1 Vol Regime", text_color=color.black)
        table.cell(debug_table, 1, 16, v1_volatility_regime, text_color=color.black)
        table.cell(debug_table, 0, 17, "V1 RSI Buy Thresh", text_color=color.black)
        table.cell(debug_table, 1, 17, str.tostring(v1_rsi_buy_threshold, "#.##"), text_color=color.green)
        table.cell(debug_table, 0, 18, "V1 RSI Sell Thresh", text_color=color.black)
        table.cell(debug_table, 1, 18, str.tostring(v1_rsi_sell_threshold, "#.##"), text_color=color.red)
        table.cell(debug_table, 0, 19, "V1 Buy Score", text_color=color.black)
        table.cell(debug_table, 1, 19, str.tostring(v1_buy_score, "#.#"), text_color=color.green)
        table.cell(debug_table, 0, 20, "V1 Sell Score", text_color=color.black)
        table.cell(debug_table, 1, 20, str.tostring(v1_sell_score, "#.#"), text_color=color.red)
        table.cell(debug_table, 0, 21, "V1 Score Diff", text_color=color.black)
        table.cell(debug_table, 1, 21, str.tostring(v1_score_diff, "#.#"), text_color=color.blue)

// =================== ALERTS (Refined) ===================
// REFINEMENT: Old alerts are removed and replaced with new state-based alerts for accuracy.
if final_buy_signal and trade_state[1] == "NONE"
    alert_message_buy = "🚀 BTC BUY Signal (" + current_signal_strength_text + ") | Confluence: " + str.tostring(v1_actual_buy_confluence_count)
    alert(alert_message_buy, alert.freq_once_per_bar_close)

if final_sell_signal and trade_state[1] == "NONE"
    alert_message_sell = "🔻 BTC SELL Signal (" + current_signal_strength_text + ") | Confluence: " + str.tostring(v1_actual_sell_confluence_count)
    alert(alert_message_sell, alert.freq_once_per_bar_close)

if exit_long or exit_short
    alert_message_exit = "❌ BTC EXIT Signal | Reason: " + exit_reason_text
    alert(alert_message_exit, alert.freq_once_per_bar_close)
